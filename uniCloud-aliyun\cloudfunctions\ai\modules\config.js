/**
 * AI 配置和常量管理模块
 *
 * 主要功能：
 * 1. 管理豆包 AI 的连接配置参数
 * 2. 维护工具注册表，定义所有可用的云函数工具
 * 3. 定义 SSE 消息类型常量，统一前后端通信协议
 * 4. 提供默认系统提示词，指导 AI 进行意图识别
 *
 * 设计原则：
 * - 集中化配置管理，便于维护和修改
 * - 类型安全的参数定义，减少运行时错误
 * - 可扩展的工具注册机制，支持动态添加新工具
 * - 标准化的消息类型定义，确保通信协议一致性
 *
 * 使用场景：
 * - 初始化 AI 客户端时获取连接参数
 * - 执行计划生成时查询可用工具
 * - SSE 推送时使用标准消息类型
 * - 意图识别时使用默认系统提示词
 *
 * <AUTHOR> 开发团队
 * @version 1.3.0
 * @since 2024-01-01
 */

const doubaoParams = {
  baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  apiKey: 'a1d08626-e9fa-425b-a8cf-e59f274d3a3d', // API 密钥
  timeout: 30000, // 请求超时时间（毫秒），30 秒超时
}

/**
 * 工具注册表
 * 定义所有可用的云函数工具及其配置信息
 * 基于 README.md 文档中定义的 8 个核心 API 方法
 */
const TOOL_REGISTRY = {
  // ==================== 任务管理工具 ====================

  /**
   * 获取任务列表工具
   * 获取符合条件的任务列表，支持多种筛选条件
   */
  getTasks: {
    type: 'builtin',
    tool: 'todo',
    method: 'getTasks',
    description: '获取符合条件的任务列表，支持多种筛选条件',
    parameters: {
      type: 'object',
      properties: {
        mode: {
          type: 'string',
          description:
            '时间筛选模式。可选值："all"（全部）、"today"（今天）、"yesterday"（昨天）、"recent_7_days"（最近 7 天）',
        },
        keyword: {
          type: 'string',
          description: '关键词搜索（搜索标题和内容）',
        },
        priority: {
          type: 'number',
          description: '优先级筛选。0=最低，1=低，3=中，5=高',
        },
        projectName: {
          type: 'string',
          description: '项目名称筛选',
        },
        completed: {
          type: 'boolean',
          description: '完成状态筛选。true=已完成，false=未完成',
        },
      },
    },
    metadata: {
      category: 'query',
    },
  },

  /**
   * 创建任务工具
   * 创建一个新的任务
   */
  createTask: {
    type: 'builtin',
    tool: 'todo',
    method: 'createTask',
    description: '创建一个新的任务',
    parameters: {
      type: 'object',
      properties: {
        title: {
          type: 'string',
          description: '任务标题（必填）',
        },
        content: {
          type: 'string',
          description: '任务详细内容',
        },
        priority: {
          type: 'number',
          description: '优先级，默认为 0。0=最低，1=低，3=中，5=高',
        },
        projectName: {
          type: 'string',
          description: '项目名称（会自动查找对应项目 ID）',
        },
        tagNames: {
          type: 'array',
          items: { type: 'string' },
          description: '标签名称数组（会自动查找对应标签 ID）',
        },
        startDate: {
          type: 'string',
          description: '开始时间，ISO 8601 格式，如：2024-01-15T09:00:00.000Z',
        },
        dueDate: {
          type: 'string',
          description: '截止时间，ISO 8601 格式，如：2024-01-20T18:00:00.000Z',
        },
        isAllDay: {
          type: 'boolean',
          description: '是否全天任务，默认为 false',
        },
        reminder: {
          type: 'string',
          description: '提醒时间，ISO 8601 格式',
        },
        kind: {
          type: 'string',
          description: '任务类型，默认为 TEXT。可选值：TEXT、CHECKLIST、NOTE',
        },
      },
      required: ['title'],
    },
    metadata: {
      category: 'action',
    },
  },

  /**
   * 更新任务工具
   * 更新现有任务的信息
   */
  updateTask: {
    type: 'builtin',
    tool: 'todo',
    method: 'updateTask',
    description: '更新现有任务的信息',
    parameters: {
      type: 'object',
      properties: {
        taskId: {
          type: 'string',
          description: '任务 ID（必填）',
        },
        updateData: {
          type: 'object',
          description: '更新数据对象',
          properties: {
            title: {
              type: 'string',
              description: '新的任务标题',
            },
            content: {
              type: 'string',
              description: '新的任务内容',
            },
            priority: {
              type: 'number',
              description: '新的优先级。0=最低，1=低，3=中，5=高',
            },
            projectName: {
              type: 'string',
              description: '新的项目名称',
            },
            tagNames: {
              type: 'array',
              items: { type: 'string' },
              description: '新的标签名称数组',
            },
            startDate: {
              type: 'string',
              description: '新的开始时间，ISO 8601 格式',
            },
            dueDate: {
              type: 'string',
              description: '新的截止时间，ISO 8601 格式',
            },
            isAllDay: {
              type: 'boolean',
              description: '是否全天任务',
            },
            status: {
              type: 'number',
              description: '任务状态。0=活跃，2=已完成',
            },
            reminder: {
              type: 'string',
              description: '提醒时间，ISO 8601 格式',
            },
          },
        },
      },
      required: ['taskId', 'updateData'],
    },
    metadata: {
      category: 'action',
    },
  },

  /**
   * 删除任务工具
   * 删除指定的任务
   */
  deleteTask: {
    type: 'builtin',
    tool: 'todo',
    method: 'deleteTask',
    description: '删除指定的任务',
    parameters: {
      type: 'object',
      properties: {
        taskId: {
          type: 'string',
          description: '要删除的任务 ID（必填）',
        },
      },
      required: ['taskId'],
    },
    metadata: {
      category: 'action',
    },
  },

  // ==================== 项目管理工具 ====================

  /**
   * 获取项目列表工具
   * 获取符合条件的项目列表
   */
  getProjects: {
    type: 'builtin',
    tool: 'todo',
    method: 'getProjects',
    description: '获取符合条件的项目列表',
    parameters: {
      type: 'object',
      properties: {
        keyword: {
          type: 'string',
          description: '关键词搜索（搜索项目名称）',
        },
        includeClosed: {
          type: 'boolean',
          description: '是否包含已关闭的项目，默认为 false',
        },
      },
    },
    metadata: {
      category: 'query',
    },
  },

  /**
   * 创建项目工具
   * 创建一个新的项目
   */
  createProject: {
    type: 'builtin',
    tool: 'todo',
    method: 'createProject',
    description: '创建一个新的项目',
    parameters: {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          description: '项目名称（必填）',
        },
        color: {
          type: 'string',
          description: '项目颜色，默认为#3498db。十六进制颜色码，如：#e74c3c',
        },
        kind: {
          type: 'string',
          description: '项目类型，默认为 TASK。可选值：TASK（任务项目）、NOTE（笔记项目）',
        },
      },
      required: ['name'],
    },
    metadata: {
      category: 'action',
    },
  },

  /**
   * 更新项目工具
   * 更新现有项目的信息
   */
  updateProject: {
    type: 'builtin',
    tool: 'todo',
    method: 'updateProject',
    description: '更新现有项目的信息',
    parameters: {
      type: 'object',
      properties: {
        projectId: {
          type: 'string',
          description: '项目 ID（必填）',
        },
        updateData: {
          type: 'object',
          description: '更新数据对象',
          properties: {
            name: {
              type: 'string',
              description: '新的项目名称',
            },
            color: {
              type: 'string',
              description: '新的项目颜色，十六进制颜色码',
            },
            kind: {
              type: 'string',
              description: '新的项目类型。可选值：TASK、NOTE',
            },
            closed: {
              type: 'boolean',
              description: '是否关闭项目',
            },
          },
        },
      },
      required: ['projectId', 'updateData'],
    },
    metadata: {
      category: 'action',
    },
  },

  /**
   * 删除项目工具
   * 删除指定的项目
   */
  deleteProject: {
    type: 'builtin',
    tool: 'todo',
    method: 'deleteProject',
    description: '删除指定的项目',
    parameters: {
      type: 'object',
      properties: {
        projectId: {
          type: 'string',
          description: '要删除的项目 ID（必填）',
        },
      },
      required: ['projectId'],
    },
    metadata: {
      category: 'action',
    },
  },
}

/**
 * SSE 消息类型常量
 * 定义所有 SSE 推送消息的类型标识，确保前后端通信协议的一致性
 *
 * 消息类型分类：
 * 1. 基础流程消息：处理用户请求的基本流程
 * 2. 意图识别消息：AI 意图解析相关的消息
 * 3. 任务执行消息：智能任务执行过程中的消息
 *
 * 使用方式：
 * - 后端推送消息时使用这些类型标识
 * - 前端根据消息类型进行不同的处理逻辑
 * - 便于消息类型的统一管理和维护
 *
 * 扩展原则：
 * - 新增消息类型时应遵循命名规范
 * - 保持向后兼容性，避免修改已有类型
 * - 添加详细的中文描述，便于理解
 */
const SSE_MESSAGE_TYPES = {
  // 基础流程消息类型
  start: '开始生成回复', // 开始处理用户请求，通知前端显示加载状态
  intent_type: '意图类型识别', // 识别到用户意图类型，让前端知道请求的性质
  intent_content_start: '意图内容开始', // 开始推送意图内容，准备接收具体内容
  intent_content_chunk: '意图内容块', // 意图内容数据块，流式推送的内容片段
  end: '结束', // 处理完成，通知前端停止等待
  error: '错误', // 处理出错，通知前端显示错误信息

  // 任务执行相关消息类型（V1.1 新增）
  execution_plan_start: '执行计划开始', // 开始执行任务计划，显示计划信息
  execution_step: '执行步骤', // 当前执行步骤信息，显示进度
  step_result: '步骤结果', // 步骤执行结果，显示每步的执行情况
  step_error: '步骤错误', // 步骤执行出错，显示具体错误信息
  execution_complete: '执行完成', // 任务执行完成，显示最终结果
  execution_failed: '执行失败', // 任务执行失败，显示失败原因
}

/**
 * 默认系统提示词
 * 用于指导 AI 进行意图识别和内容提取的核心提示词
 *
 * 设计目标：
 * 1. 准确识别用户的真实意图
 * 2. 标准化 AI 的输出格式
 * 3. 确保后续处理的可靠性
 *
 * 意图类型定义（简化为两种）：
 * - task: 任务操作意图，包括创建、查询、修改、删除等所有任务相关操作
 * - chat: 普通聊天意图，不涉及具体的任务操作
 *
 * 输出格式要求：
 * - 使用中文引号「」标识关键字段
 * - task 类型只输出意图类型，不输出闲聊回复
 * - chat 类型需要输出闲聊回复内容
 * - 不允许添加额外的解释或内容
 *
 * 关键词匹配规则：
 * - 任务类：创建、添加、设置、安排、新建、制定、查询、搜索、查看、显示、列出、找到、修改、更新、删除、完成、统计等
 * - 聊天类：问候、询问、讨论、解释等非任务操作
 */
const DEFAULT_SYSTEM_PROMPT = `你是一个专业的任务分析助手。你的主要职责是理解用户输入的内容，并判断用户的意图类型。

请分析用户输入内容，并将其归类为以下两种意图之一：
1. task: 当用户想要执行任何与任务相关的操作时（包括创建、查询、修改、删除任务等）
2. chat: 其他所有不属于任务操作的内容，视为一般闲聊对话

分析完成后，必须严格按照以下格式输出结果：
- 如果是 task 类型，只输出：「意图类型」：task
- 如果是 chat 类型，输出：
  「意图类型」：chat
  「闲聊回复」：[针对用户问题的回复内容]

注意：
- 分析要准确，不要混淆不同意图类型
- task 类型只输出一行意图类型，不需要额外内容
- chat 类型需要输出两行：意图类型和闲聊回复
- 确保格式严格遵循示例，包括使用中文引号「」
- task 类型包括：创建任务、查询任务、修改任务、删除任务、任务统计等所有任务相关操作
- chat 类型包括：问候、闲聊、询问非任务相关问题、讨论、解释等`

module.exports = {
  TOOL_REGISTRY,
  SSE_MESSAGE_TYPES,
  DEFAULT_SYSTEM_PROMPT,
  doubaoParams,
}
